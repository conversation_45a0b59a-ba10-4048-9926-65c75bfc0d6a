// 防止头文件重复包含的宏定义开始
#ifndef __SAMPLE_H__
// 定义头文件标识符，确保只被包含一次
#define __SAMPLE_H__

// 包含公共头文件，提供基础数据类型和宏定义
#include "common.h"


// 每周期采样次数宏定义，从定时器2正弦周期计数获取
#define SAMPLES_1P (TIM2_SIN_PRD_CNT)

// X个周期数宏定义，用于多周期平均计算，设为10个周期
#define SAMPLES_XP 10

// 指数移动平均滤波系数定义
#define AC_EMA_Multiplier (1.0f / 1.0f)  // 交流信号EMA滤波系数，当前设为1.0（无滤波）
#define DC_EMA_Multiplier (1.0f / 1.0f)  // 直流信号EMA滤波系数，当前设为1.0（无滤波）

// 交流电采样处理结构体定义
typedef struct _Sample_AC_HandleTypeDef
{
    // 输出结果变量
    float out_rms_xp;  // 多周期（SAMPLES_XP个周期）的均方根值输出
    float out_rms_1p;  // 单个周期的均方根值输出
    float out_instant; // 当前瞬时值输出

    // 归一化相关变量
    float out_pu_value; // 归一化后的输出值（per unit值）
    float calc_pu_base; // 归一化计算的基准值
    float calc_pu_k;    // 归一化计算系数

    // 线性校准参数
    float calc_slope;     // ADC值转换为实际值的斜率系数
    float calc_intercept; // ADC值转换为实际值的截距偏移

    // ADC采样相关变量
    uint32_t in_adc_value; // 当前输入的ADC原始采样值
    float adc_value_f;     // 经过滤波处理后的ADC值（浮点型）

    // 均值计算相关变量
    uint32_t calc_adc_sum; // ADC采样值累加和，用于计算平均值
    float adc_avg;         // ADC采样的平均值，用于去除直流偏置

    // 采样计数和方差计算变量
    uint32_t sample_count; // 当前周期内的采样计数器
    float calc_sq_sum;     // 瞬时值平方和，用于计算均方根值

    // 多周期RMS计算相关变量
    uint32_t calc_rms_xp_count; // 多周期RMS计算的周期计数器
    float calc_rms_xp_sum;      // 多周期RMS值的累加和

    // 完成标志位
    uint32_t flag_completed_1p; // 单周期计算完成标志
    uint32_t flag_completed_xp; // 多周期计算完成标志

    // 采样缓冲区
    uint32_t adc_buf_idx;         // 采样缓冲区的当前索引位置
    uint16_t adc_buf[SAMPLES_1P]; // 存储一个周期内所有采样值的缓冲区数组

} Sample_AC_HandleTypeDef; // 交流采样处理结构体类型定义结束

// 直流电采样处理结构体定义
typedef struct _Sample_DC_HandleTypeDef
{
    float out_instant; // 直流瞬时值输出

    uint32_t in_adc_value; // 输入的ADC原始采样值

    float calc_adc_value_f; // 经过滤波处理后的ADC值（浮点型）
    float calc_slope;       // ADC值转换为实际值的斜率系数
    float calc_intercept;   // ADC值转换为实际值的截距偏移

} Sample_DC_HandleTypeDef; // 直流采样处理结构体类型定义结束

// 交流采样结构体成员初始化函数声明
void Sample_Membe_Init(Sample_AC_HandleTypeDef *h, float pu_base, float calc_slope, float calc_intercept);
// 交流采样计算处理函数声明
void Sample_AC_Calculate(Sample_AC_HandleTypeDef *hACS, uint16_t adc_value);

// 直流采样结构体初始化函数声明
void Sample_DC_Init(Sample_DC_HandleTypeDef *h, float calc_slope, float calc_intercept);
// 直流采样计算处理函数声明
void Sample_DC_Calculate(Sample_DC_HandleTypeDef *hDCS, uint16_t adc_value);

// 交流采样计算宏定义，用于快速执行采样计算过程
#define SAMPLE_AC_CALCULATE_MACRO(h, adc_v)                                           \
    {                                                                                 \
        /* 对ADC值进行低通滤波：当前值右移1位(除以2)加上新值右移5位(除以32) */        \
        h.in_adc_value = ( h.in_adc_value>>1)+ ((adc_v) >> 5);                       \
        /* 累加ADC值，用于后续计算平均值 */                                           \
        h.calc_adc_sum += h.in_adc_value;                                             \
                                                                                      \
        /* 指数移动平均滤波：新值 = 旧值 + (当前值-旧值) * 滤波系数 */                \
        h.adc_value_f += ((float)h.in_adc_value - h.adc_value_f) * AC_EMA_Multiplier; \
        /* 计算瞬时值：(滤波后ADC值 - 平均值) * 斜率系数，去除直流偏置 */             \
        h.out_instant = (h.adc_value_f - h.adc_avg) * h.calc_slope;                   \
                                                                                      \
        /* 累加瞬时值的平方，用于计算均方根值 */                                      \
        h.calc_sq_sum += h.out_instant * h.out_instant;                               \
        /* 采样计数器递增 */                                                         \
        h.sample_count++;                                                             \
                                                                                      \
        /* 将ADC值存入缓冲区，索引右移1位是为了降低存储频率 */                        \
        h.adc_buf[h.adc_buf_idx >> 1] = h.in_adc_value;                               \
        /* 缓冲区索引递增，达到一个周期后回零 */                                      \
        if (++h.adc_buf_idx >= SAMPLES_1P)                                            \
            h.adc_buf_idx = 0;                                                        \
    }

// 交流RMS（均方根值）计算宏定义，用于计算单周期和多周期RMS值
#define SAMPLE_AC_RMS_CALCULATE_MACRO(h, adc_v)                                               \
    {                                                                                         \
        /* 检查是否已采集够一个周期的样本数 */                                               \
        if (h.sample_count >= SAMPLES_1P)                                                     \
        {                                                                                     \
            /* 计算ADC平均值，用于去除直流偏置 */                                            \
            h.adc_avg = h.calc_adc_sum / h.sample_count;                                      \
                                                                                              \
            /* 计算单周期RMS值：sqrt(平方和/采样数) + 截距补偿 */                             \
            h.out_rms_1p = (sqrtf(h.calc_sq_sum / (float)h.sample_count)) + h.calc_intercept; \
            /* 清零累加器，为下一个周期做准备 */                                             \
            h.calc_sq_sum = 0, h.sample_count = 0, h.calc_adc_sum = 0;                        \
                                                                                              \
            /* 设置单周期计算完成标志 */                                                     \
            h.flag_completed_1p = 1;                                                          \
                                                                                              \
            /* 将单周期RMS值累加到多周期计算中 */                                            \
            h.calc_rms_xp_sum += h.out_rms_1p;                                                \
            /* 检查是否已完成指定数量的周期 */                                               \
            if (++h.calc_rms_xp_count >= SAMPLES_XP)                                          \
            {                                                                                 \
                /* 重置多周期计数器 */                                                       \
                h.calc_rms_xp_count = 0;                                                      \
                                                                                              \
                /* 计算多周期平均RMS值 */                                                    \
                h.out_rms_xp = h.calc_rms_xp_sum / (float)SAMPLES_XP;                         \
                /* 清零多周期累加器 */                                                       \
                h.calc_rms_xp_sum = 0;                                                        \
                /* 设置多周期计算完成标志 */                                                 \
                h.flag_completed_xp = 1;                                                      \
            }                                                                                 \
        }                                                                                     \
    }

// 直流采样计算宏定义，用于快速执行直流信号的采样计算
#define SAMPLE_DC_CALCULATE_MACRO(h, adc_v)                                                     \
    {                                                                                           \
        /* 对ADC值进行低通滤波：当前值右移1位(除以2)加上新值右移5位(除以32) */                \
        h.in_adc_value = ( h.in_adc_value>>1)+ ((adc_v) >> 5);                                 \
                                                                                                \
        /* 指数移动平均滤波：新值 = 旧值 + (当前值-旧值) * 滤波系数 */                        \
        h.calc_adc_value_f += ((float)h.in_adc_value - h.calc_adc_value_f) * DC_EMA_Multiplier; \
                                                                                                \
        /* 计算直流瞬时值：滤波后ADC值 * 斜率 + 截距 */                                        \
        h.out_instant = h.calc_adc_value_f * h.calc_slope + h.calc_intercept;                   \
        /* 限制输出值不能为负数（直流信号通常为正值） */                                      \
        if (h.out_instant < 0)                                                                  \
        {                                                                                       \
            h.out_instant = 0;  /* 负值时强制设为0 */                                          \
        }                                                                                       \
    }

// 防止头文件重复包含的宏定义结束
#endif // __SAMPLE_H__
